# AI/ML Model Training Infrastructure Documentation

## Overview

The model training infrastructure provides a comprehensive platform for developing, training, and deploying machine learning models for GitInsight. This system supports experiment tracking, hyperparameter optimization, distributed training, model versioning, and automated deployment pipelines to ensure reproducible and scalable ML operations.

## Training Infrastructure Architecture

### Training Pipeline Overview

```
┌─────────────────────────────────────────────────────────────────┐
│                   Model Training Infrastructure                  │
├─────────────────────────────────────────────────────────────────┤
│                                                                 │
│  ┌─────────────────────────────────────────────────────────────┐  │
│  │                  Experiment Management                       │  │
│  │                                                             │  │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────────────┐  │  │
│  │  │   MLflow    │  │  Weights &  │  │     Experiment      │  │  │
│  │  │  Tracking   │  │   Biases    │  │    Configuration    │  │  │
│  │  │             │  │             │  │                     │  │  │
│  │  │ • Metrics   │  │ • Visualize │  │ • Hyperparameters   │  │  │
│  │  │ • Artifacts │  │ • Compare   │  │ • Data Versions     │  │  │
│  │  │ • Models    │  │ • Collaborate│ │ • Environment       │  │  │
│  │  │ • Lineage   │  │ • Reports   │  │ • Reproducibility   │  │  │
│  │  └─────────────┘  └─────────────┘  └─────────────────────┘  │  │
│  └─────────────────────────────────────────────────────────────┘  │
│           │                                                     │
│           ▼                                                     │
│  ┌─────────────────────────────────────────────────────────────┐  │
│  │                   Training Orchestration                    │  │
│  │                                                             │  │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────────────┐  │  │
│  │  │   Kubeflow  │  │    Ray      │  │      Airflow        │  │  │
│  │  │  Pipelines  │  │   Train     │  │    Workflows        │  │  │
│  │  │             │  │             │  │                     │  │  │
│  │  │ • DAG       │  │ • Distributed│ │ • Scheduling        │  │  │
│  │  │ • Steps     │  │ • Scaling   │  │ • Dependencies      │  │  │
│  │  │ • Artifacts │  │ • Fault Tol │  │ • Monitoring        │  │  │
│  │  │ • Metadata  │  │ • Resource  │  │ • Retry Logic       │  │  │
│  │  └─────────────┘  └─────────────┘  └─────────────────────┘  │  │
│  └─────────────────────────────────────────────────────────────┘  │
│           │                                                     │
│           ▼                                                     │
│  ┌─────────────────────────────────────────────────────────────┐  │
│  │                    Training Execution                       │  │
│  │                                                             │  │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────────────┐  │  │
│  │  │   PyTorch   │  │ TensorFlow  │  │     Hugging         │  │  │
│  │  │  Lightning  │  │   Extended  │  │      Face           │  │  │
│  │  │             │  │             │  │                     │  │  │
│  │  │ • Trainers  │  │ • Estimators│  │ • Transformers      │  │  │
│  │  │ • Callbacks │  │ • Strategies│  │ • Datasets          │  │  │
│  │  │ • Loggers   │  │ • Distribute│  │ • Tokenizers        │  │  │
│  │  │ • Profilers │  │ • Checkpts  │  │ • Fine-tuning       │  │  │
│  │  └─────────────┘  └─────────────┘  └─────────────────────┘  │  │
│  └─────────────────────────────────────────────────────────────┘  │
│           │                                                     │
│           ▼                                                     │
│  ┌─────────────────────────────────────────────────────────────┐  │
│  │                   Model Registry & Deployment               │  │
│  │                                                             │  │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────────────┐  │  │
│  │  │   Model     │  │   Model     │  │      Deployment     │  │  │
│  │  │  Registry   │  │ Validation  │  │     Pipeline        │  │  │
│  │  │             │  │             │  │                     │  │  │
│  │  │ • Versioning│  │ • A/B Test  │  │ • Staging           │  │  │
│  │  │ • Metadata  │  │ • Metrics   │  │ • Production        │  │  │
│  │  │ • Lineage   │  │ • Approval  │  │ • Rollback          │  │  │
│  │  │ • Governance│  │ • Quality   │  │ • Monitoring        │  │  │
│  │  └─────────────┘  └─────────────┘  └─────────────────────┘  │  │
│  └─────────────────────────────────────────────────────────────┘  │
└─────────────────────────────────────────────────────────────────┘
```

## Experiment Management

### MLflow Integration

```python
# aiml/training/experiment_manager.py
import mlflow
import mlflow.pytorch
import mlflow.sklearn
from typing import Dict, Any, Optional, List
import json
import os
from pathlib import Path
import torch
import numpy as np

class ExperimentManager:
    """Manages ML experiments with MLflow tracking."""
    
    def __init__(self, tracking_uri: str, experiment_name: str):
        mlflow.set_tracking_uri(tracking_uri)
        self.experiment_name = experiment_name
        self.experiment = mlflow.set_experiment(experiment_name)
        self.current_run = None
    
    def start_run(self, run_name: Optional[str] = None, 
                  tags: Optional[Dict[str, str]] = None) -> mlflow.ActiveRun:
        """Start a new MLflow run."""
        self.current_run = mlflow.start_run(run_name=run_name, tags=tags)
        return self.current_run
    
    def log_params(self, params: Dict[str, Any]):
        """Log hyperparameters."""
        for key, value in params.items():
            mlflow.log_param(key, value)
    
    def log_metrics(self, metrics: Dict[str, float], step: Optional[int] = None):
        """Log training metrics."""
        for key, value in metrics.items():
            mlflow.log_metric(key, value, step=step)
    
    def log_model(self, model: Any, model_name: str, 
                  framework: str = "pytorch", 
                  signature: Optional[mlflow.models.ModelSignature] = None):
        """Log trained model."""
        if framework == "pytorch":
            mlflow.pytorch.log_model(
                model, 
                model_name, 
                signature=signature,
                registered_model_name=f"{self.experiment_name}_{model_name}"
            )
        elif framework == "sklearn":
            mlflow.sklearn.log_model(
                model, 
                model_name, 
                signature=signature,
                registered_model_name=f"{self.experiment_name}_{model_name}"
            )
    
    def log_artifacts(self, artifacts: Dict[str, Any]):
        """Log training artifacts."""
        for name, artifact in artifacts.items():
            if isinstance(artifact, (dict, list)):
                # Save as JSON
                artifact_path = f"{name}.json"
                with open(artifact_path, 'w') as f:
                    json.dump(artifact, f, indent=2)
                mlflow.log_artifact(artifact_path)
                os.remove(artifact_path)
            elif isinstance(artifact, np.ndarray):
                # Save as numpy array
                artifact_path = f"{name}.npy"
                np.save(artifact_path, artifact)
                mlflow.log_artifact(artifact_path)
                os.remove(artifact_path)
            elif isinstance(artifact, str) and os.path.exists(artifact):
                # Log file
                mlflow.log_artifact(artifact)
    
    def end_run(self):
        """End current MLflow run."""
        if self.current_run:
            mlflow.end_run()
            self.current_run = None

class TrainingConfig:
    """Configuration for training experiments."""
    
    def __init__(self, config_dict: Dict[str, Any]):
        self.model_type = config_dict['model_type']
        self.model_config = config_dict['model_config']
        self.training_config = config_dict['training_config']
        self.data_config = config_dict['data_config']
        self.experiment_config = config_dict.get('experiment_config', {})
        
        # Validate configuration
        self._validate_config()
    
    def _validate_config(self):
        """Validate training configuration."""
        required_fields = {
            'model_type': str,
            'model_config': dict,
            'training_config': dict,
            'data_config': dict
        }
        
        for field, expected_type in required_fields.items():
            if not hasattr(self, field):
                raise ValueError(f"Missing required field: {field}")
            if not isinstance(getattr(self, field), expected_type):
                raise ValueError(f"Field {field} must be of type {expected_type}")
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert config to dictionary."""
        return {
            'model_type': self.model_type,
            'model_config': self.model_config,
            'training_config': self.training_config,
            'data_config': self.data_config,
            'experiment_config': self.experiment_config
        }
```

### Training Orchestration

```python
# aiml/training/trainer.py
import torch
import torch.nn as nn
from torch.utils.data import DataLoader
import pytorch_lightning as pl
from pytorch_lightning.callbacks import ModelCheckpoint, EarlyStopping
from pytorch_lightning.loggers import MLFlowLogger
from typing import Dict, Any, Optional, List, Tuple
import numpy as np
from sklearn.metrics import accuracy_score, precision_recall_fscore_support

class BaseTrainer(pl.LightningModule):
    """Base trainer class for all models."""
    
    def __init__(self, model: nn.Module, config: TrainingConfig):
        super().__init__()
        self.model = model
        self.config = config
        self.save_hyperparameters(config.to_dict())
        
        # Training configuration
        self.learning_rate = config.training_config.get('learning_rate', 1e-3)
        self.weight_decay = config.training_config.get('weight_decay', 1e-4)
        self.scheduler_config = config.training_config.get('scheduler', {})
        
        # Metrics tracking
        self.training_step_outputs = []
        self.validation_step_outputs = []
    
    def configure_optimizers(self):
        """Configure optimizers and schedulers."""
        optimizer = torch.optim.AdamW(
            self.parameters(),
            lr=self.learning_rate,
            weight_decay=self.weight_decay
        )
        
        if self.scheduler_config:
            scheduler_type = self.scheduler_config.get('type', 'cosine')
            
            if scheduler_type == 'cosine':
                scheduler = torch.optim.lr_scheduler.CosineAnnealingLR(
                    optimizer,
                    T_max=self.scheduler_config.get('T_max', 100),
                    eta_min=self.scheduler_config.get('eta_min', 1e-6)
                )
            elif scheduler_type == 'step':
                scheduler = torch.optim.lr_scheduler.StepLR(
                    optimizer,
                    step_size=self.scheduler_config.get('step_size', 30),
                    gamma=self.scheduler_config.get('gamma', 0.1)
                )
            else:
                scheduler = torch.optim.lr_scheduler.ReduceLROnPlateau(
                    optimizer,
                    mode='min',
                    factor=0.5,
                    patience=10
                )
            
            return {
                'optimizer': optimizer,
                'lr_scheduler': {
                    'scheduler': scheduler,
                    'monitor': 'val_loss',
                    'interval': 'epoch',
                    'frequency': 1
                }
            }
        
        return optimizer
    
    def training_step(self, batch: Dict[str, torch.Tensor], batch_idx: int) -> torch.Tensor:
        """Training step."""
        outputs = self.model(**batch)
        loss = outputs['loss']
        
        # Log metrics
        self.log('train_loss', loss, on_step=True, on_epoch=True, prog_bar=True)
        
        # Store outputs for epoch-level metrics
        self.training_step_outputs.append({
            'loss': loss.detach(),
            'predictions': outputs.get('predictions'),
            'targets': batch.get('labels')
        })
        
        return loss
    
    def validation_step(self, batch: Dict[str, torch.Tensor], batch_idx: int) -> torch.Tensor:
        """Validation step."""
        outputs = self.model(**batch)
        loss = outputs['loss']
        
        # Log metrics
        self.log('val_loss', loss, on_step=False, on_epoch=True, prog_bar=True)
        
        # Store outputs for epoch-level metrics
        self.validation_step_outputs.append({
            'loss': loss.detach(),
            'predictions': outputs.get('predictions'),
            'targets': batch.get('labels')
        })
        
        return loss
    
    def on_train_epoch_end(self):
        """Calculate and log epoch-level training metrics."""
        if self.training_step_outputs:
            avg_loss = torch.stack([x['loss'] for x in self.training_step_outputs]).mean()
            self.log('train_epoch_loss', avg_loss)
            
            # Calculate additional metrics if predictions available
            if all('predictions' in x for x in self.training_step_outputs):
                predictions = torch.cat([x['predictions'] for x in self.training_step_outputs])
                targets = torch.cat([x['targets'] for x in self.training_step_outputs])
                
                metrics = self._calculate_metrics(predictions, targets, prefix='train')
                for name, value in metrics.items():
                    self.log(name, value)
            
            self.training_step_outputs.clear()
    
    def on_validation_epoch_end(self):
        """Calculate and log epoch-level validation metrics."""
        if self.validation_step_outputs:
            avg_loss = torch.stack([x['loss'] for x in self.validation_step_outputs]).mean()
            self.log('val_epoch_loss', avg_loss)
            
            # Calculate additional metrics if predictions available
            if all('predictions' in x for x in self.validation_step_outputs):
                predictions = torch.cat([x['predictions'] for x in self.validation_step_outputs])
                targets = torch.cat([x['targets'] for x in self.validation_step_outputs])
                
                metrics = self._calculate_metrics(predictions, targets, prefix='val')
                for name, value in metrics.items():
                    self.log(name, value)
            
            self.validation_step_outputs.clear()
    
    def _calculate_metrics(self, predictions: torch.Tensor, 
                          targets: torch.Tensor, prefix: str) -> Dict[str, float]:
        """Calculate classification metrics."""
        # Convert to numpy for sklearn metrics
        if predictions.dim() > 1:
            pred_labels = torch.argmax(predictions, dim=1).cpu().numpy()
        else:
            pred_labels = (predictions > 0.5).cpu().numpy()
        
        true_labels = targets.cpu().numpy()
        
        # Calculate metrics
        accuracy = accuracy_score(true_labels, pred_labels)
        precision, recall, f1, _ = precision_recall_fscore_support(
            true_labels, pred_labels, average='weighted', zero_division=0
        )
        
        return {
            f'{prefix}_accuracy': accuracy,
            f'{prefix}_precision': precision,
            f'{prefix}_recall': recall,
            f'{prefix}_f1': f1
        }

class TrainingPipeline:
    """Orchestrates the complete training pipeline."""
    
    def __init__(self, config: TrainingConfig, experiment_manager: ExperimentManager):
        self.config = config
        self.experiment_manager = experiment_manager
        self.trainer = None
        self.model = None
    
    def run_training(self, train_dataloader: DataLoader, 
                    val_dataloader: DataLoader,
                    model_class: type) -> Dict[str, Any]:
        """Run complete training pipeline."""
        
        # Start MLflow run
        run_name = f"{self.config.model_type}_{self.config.experiment_config.get('run_id', 'default')}"
        self.experiment_manager.start_run(run_name=run_name)
        
        try:
            # Log configuration
            self.experiment_manager.log_params(self.config.to_dict())
            
            # Initialize model
            self.model = model_class(self.config.model_config)
            
            # Initialize trainer
            trainer_module = BaseTrainer(self.model, self.config)
            
            # Setup callbacks
            callbacks = self._setup_callbacks()
            
            # Setup logger
            mlflow_logger = MLFlowLogger(
                experiment_name=self.experiment_manager.experiment_name,
                run_id=self.experiment_manager.current_run.info.run_id
            )
            
            # Initialize PyTorch Lightning trainer
            self.trainer = pl.Trainer(
                max_epochs=self.config.training_config.get('max_epochs', 100),
                callbacks=callbacks,
                logger=mlflow_logger,
                accelerator='gpu' if torch.cuda.is_available() else 'cpu',
                devices=1,
                precision=self.config.training_config.get('precision', 32),
                gradient_clip_val=self.config.training_config.get('gradient_clip_val', 1.0),
                accumulate_grad_batches=self.config.training_config.get('accumulate_grad_batches', 1)
            )
            
            # Train model
            self.trainer.fit(trainer_module, train_dataloader, val_dataloader)
            
            # Log final model
            self.experiment_manager.log_model(
                trainer_module.model,
                self.config.model_type,
                framework="pytorch"
            )
            
            # Get training results
            results = {
                'best_val_loss': self.trainer.callback_metrics.get('val_loss', float('inf')),
                'best_val_accuracy': self.trainer.callback_metrics.get('val_accuracy', 0.0),
                'total_epochs': self.trainer.current_epoch + 1,
                'model_path': self.trainer.checkpoint_callback.best_model_path
            }
            
            # Log final metrics
            self.experiment_manager.log_metrics(results)
            
            return results
            
        finally:
            # End MLflow run
            self.experiment_manager.end_run()
    
    def _setup_callbacks(self) -> List[pl.Callback]:
        """Setup training callbacks."""
        callbacks = []
        
        # Model checkpointing
        checkpoint_callback = ModelCheckpoint(
            monitor='val_loss',
            mode='min',
            save_top_k=3,
            save_last=True,
            filename='{epoch}-{val_loss:.2f}-{val_accuracy:.2f}'
        )
        callbacks.append(checkpoint_callback)
        
        # Early stopping
        if self.config.training_config.get('early_stopping', True):
            early_stop_callback = EarlyStopping(
                monitor='val_loss',
                mode='min',
                patience=self.config.training_config.get('early_stopping_patience', 10),
                verbose=True
            )
            callbacks.append(early_stop_callback)
        
        return callbacks
```

## Hyperparameter Optimization

### Optuna Integration

```python
# aiml/training/hyperparameter_optimization.py
import optuna
from optuna.integration import MLflowCallback
from typing import Dict, Any, Callable, Optional
import torch
from torch.utils.data import DataLoader

class HyperparameterOptimizer:
    """Hyperparameter optimization using Optuna."""
    
    def __init__(self, study_name: str, storage_url: Optional[str] = None):
        self.study_name = study_name
        self.storage_url = storage_url
        self.study = None
    
    def create_study(self, direction: str = 'minimize', 
                    sampler: Optional[optuna.samplers.BaseSampler] = None) -> optuna.Study:
        """Create or load Optuna study."""
        if sampler is None:
            sampler = optuna.samplers.TPESampler(seed=42)
        
        self.study = optuna.create_study(
            study_name=self.study_name,
            storage=self.storage_url,
            direction=direction,
            sampler=sampler,
            load_if_exists=True
        )
        
        return self.study
    
    def optimize(self, objective_func: Callable, n_trials: int = 100,
                timeout: Optional[int] = None) -> optuna.Study:
        """Run hyperparameter optimization."""
        if self.study is None:
            self.create_study()
        
        # Add MLflow callback for tracking
        mlflow_callback = MLflowCallback(
            tracking_uri="http://localhost:5000",
            metric_name="val_loss"
        )
        
        self.study.optimize(
            objective_func,
            n_trials=n_trials,
            timeout=timeout,
            callbacks=[mlflow_callback]
        )
        
        return self.study
    
    def suggest_hyperparameters(self, trial: optuna.Trial) -> Dict[str, Any]:
        """Suggest hyperparameters for a trial."""
        return {
            'learning_rate': trial.suggest_float('learning_rate', 1e-5, 1e-2, log=True),
            'batch_size': trial.suggest_categorical('batch_size', [16, 32, 64, 128]),
            'weight_decay': trial.suggest_float('weight_decay', 1e-6, 1e-3, log=True),
            'dropout_rate': trial.suggest_float('dropout_rate', 0.1, 0.5),
            'hidden_size': trial.suggest_categorical('hidden_size', [128, 256, 512, 1024]),
            'num_layers': trial.suggest_int('num_layers', 2, 6),
            'scheduler_type': trial.suggest_categorical('scheduler_type', ['cosine', 'step', 'plateau'])
        }

def create_objective_function(base_config: TrainingConfig,
                            train_dataloader: DataLoader,
                            val_dataloader: DataLoader,
                            model_class: type,
                            experiment_manager: ExperimentManager) -> Callable:
    """Create objective function for hyperparameter optimization."""
    
    def objective(trial: optuna.Trial) -> float:
        # Get suggested hyperparameters
        optimizer = HyperparameterOptimizer("code_quality_optimization")
        suggested_params = optimizer.suggest_hyperparameters(trial)
        
        # Update configuration with suggested parameters
        config_dict = base_config.to_dict()
        config_dict['training_config'].update(suggested_params)
        config_dict['model_config'].update({
            'hidden_size': suggested_params['hidden_size'],
            'num_layers': suggested_params['num_layers'],
            'dropout_rate': suggested_params['dropout_rate']
        })
        
        trial_config = TrainingConfig(config_dict)
        
        # Run training
        pipeline = TrainingPipeline(trial_config, experiment_manager)
        results = pipeline.run_training(train_dataloader, val_dataloader, model_class)
        
        # Return metric to optimize
        return results['best_val_loss']
    
    return objective
```

This model training infrastructure provides a robust, scalable platform for developing and deploying machine learning models with comprehensive experiment tracking, hyperparameter optimization, and automated deployment capabilities.
