# AI/ML Integration Patterns Documentation

## Overview

This document describes the integration patterns between AI/ML services and the backend APIs in GitInsight. These patterns ensure seamless communication, efficient data flow, robust error handling, and scalable processing while maintaining system reliability and performance.

## Integration Architecture

### Service Integration Overview

```
┌─────────────────────────────────────────────────────────────────┐
│                AI/ML Backend Integration Patterns               │
├─────────────────────────────────────────────────────────────────┤
│                                                                 │
│  ┌─────────────────────────────────────────────────────────────┐  │
│  │                   API Gateway Layer                         │  │
│  │                                                             │  │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────────────┐  │  │
│  │  │   FastAPI   │  │   Request   │  │     Response        │  │  │
│  │  │  Endpoints  │  │ Validation  │  │    Formatting       │  │  │
│  │  │             │  │             │  │                     │  │  │
│  │  │ • REST API  │  │ • Schema    │  │ • JSON/Protobuf     │  │  │
│  │  │ • GraphQL   │  │ • Auth      │  │ • Error Handling    │  │  │
│  │  │ • WebSocket │  │ • Rate Limit│  │ • Pagination        │  │  │
│  │  │ • Streaming │  │ • Logging   │  │ • Caching           │  │  │
│  │  └─────────────┘  └─────────────┘  └─────────────────────┘  │  │
│  └─────────────────────────────────────────────────────────────┘  │
│           │                                                     │
│           ▼                                                     │
│  ┌─────────────────────────────────────────────────────────────┐  │
│  │                 Async Processing Layer                      │  │
│  │                                                             │  │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────────────┐  │  │
│  │  │   Celery    │  │    Redis    │  │      Message        │  │  │
│  │  │   Tasks     │  │   Broker    │  │      Queues         │  │  │
│  │  │             │  │             │  │                     │  │  │
│  │  │ • Analysis  │  │ • Task Queue│  │ • RabbitMQ          │  │  │
│  │  │ • Training  │  │ • Results   │  │ • Apache Kafka      │  │  │
│  │  │ • Inference │  │ • Caching   │  │ • Event Streaming   │  │  │
│  │  │ • Monitoring│  │ • Sessions  │  │ • Dead Letter       │  │  │
│  │  └─────────────┘  └─────────────┘  └─────────────────────┘  │  │
│  └─────────────────────────────────────────────────────────────┘  │
│           │                                                     │
│           ▼                                                     │
│  ┌─────────────────────────────────────────────────────────────┐  │
│  │                   Data Flow Layer                           │  │
│  │                                                             │  │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────────────┐  │  │
│  │  │   ETL       │  │   Feature   │  │      Result         │  │  │
│  │  │ Pipelines   │  │   Store     │  │     Storage         │  │  │
│  │  │             │  │             │  │                     │  │  │
│  │  │ • Extract   │  │ • Features  │  │ • PostgreSQL        │  │  │
│  │  │ • Transform │  │ • Versions  │  │ • MongoDB           │  │  │
│  │  │ • Load      │  │ • Serving   │  │ • S3/MinIO          │  │  │
│  │  │ • Validate  │  │ • Lineage   │  │ • Time Series DB    │  │  │
│  │  └─────────────┘  └─────────────┘  └─────────────────────┘  │  │
│  └─────────────────────────────────────────────────────────────┘  │
│           │                                                     │
│           ▼                                                     │
│  ┌─────────────────────────────────────────────────────────────┐  │
│  │                 Monitoring & Observability                  │  │
│  │                                                             │  │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────────────┐  │  │
│  │  │   Metrics   │  │   Logging   │  │      Tracing        │  │  │
│  │  │ Collection  │  │ Aggregation │  │     & Debugging     │  │  │
│  │  │             │  │             │  │                     │  │  │
│  │  │ • Prometheus│  │ • ELK Stack │  │ • Jaeger            │  │  │
│  │  │ • Custom    │  │ • Structured│  │ • OpenTelemetry     │  │  │
│  │  │ • Alerts    │  │ • Centralized│ │ • Performance       │  │  │
│  │  │ • Dashboards│  │ • Searchable│  │ • Error Tracking    │  │  │
│  │  └─────────────┘  └─────────────┘  └─────────────────────┘  │  │
│  └─────────────────────────────────────────────────────────────┘  │
└─────────────────────────────────────────────────────────────────┘
```

## Async Processing Patterns

### Celery Task Integration

```python
# backend/app/tasks/aiml_tasks.py
from celery import Celery, Task
from typing import Dict, Any, List, Optional
import asyncio
import logging
import json
from datetime import datetime, timedelta
import requests
from sqlalchemy.orm import Session

from app.core.database import get_db
from app.models.repository import Repository
from app.models.insight import Insight, InsightType
from app.services.github import GitHubService
from app.core.config import settings

# Celery app configuration
celery_app = Celery(
    "gitinsight_aiml",
    broker=settings.REDIS_URL,
    backend=settings.REDIS_URL,
    include=["app.tasks.aiml_tasks"]
)

celery_app.conf.update(
    task_serializer="json",
    accept_content=["json"],
    result_serializer="json",
    timezone="UTC",
    enable_utc=True,
    task_track_started=True,
    task_time_limit=30 * 60,  # 30 minutes
    task_soft_time_limit=25 * 60,  # 25 minutes
    worker_prefetch_multiplier=1,
    task_routes={
        'app.tasks.aiml_tasks.analyze_repository': {'queue': 'analysis'},
        'app.tasks.aiml_tasks.train_model': {'queue': 'training'},
        'app.tasks.aiml_tasks.batch_inference': {'queue': 'inference'}
    }
)

class CallbackTask(Task):
    """Base task class with callback support."""
    
    def on_success(self, retval, task_id, args, kwargs):
        """Called on task success."""
        self.send_callback('success', retval, task_id, args, kwargs)
    
    def on_failure(self, exc, task_id, args, kwargs, einfo):
        """Called on task failure."""
        self.send_callback('failure', str(exc), task_id, args, kwargs)
    
    def send_callback(self, status, result, task_id, args, kwargs):
        """Send callback to webhook or WebSocket."""
        callback_url = kwargs.get('callback_url')
        if callback_url:
            try:
                requests.post(callback_url, json={
                    'task_id': task_id,
                    'status': status,
                    'result': result,
                    'timestamp': datetime.utcnow().isoformat()
                }, timeout=10)
            except Exception as e:
                logging.warning(f"Callback failed: {e}")

@celery_app.task(bind=True, base=CallbackTask, name='analyze_repository')
def analyze_repository(self, repository_id: str, analysis_options: Dict[str, Any] = None,
                      callback_url: Optional[str] = None) -> Dict[str, Any]:
    """Analyze repository using AI/ML services."""
    
    # Update task state
    self.update_state(state='PROGRESS', meta={'step': 'initializing'})
    
    try:
        # Get database session
        db = next(get_db())
        
        # Fetch repository
        repository = db.query(Repository).filter(Repository.id == repository_id).first()
        if not repository:
            raise ValueError(f"Repository {repository_id} not found")
        
        # Update repository status
        repository.analysis_status = 'in_progress'
        repository.analysis_started_at = datetime.utcnow()
        db.commit()
        
        # Fetch GitHub data
        self.update_state(state='PROGRESS', meta={'step': 'fetching_data'})
        github_service = GitHubService()
        github_data = asyncio.run(github_service.fetch_repository_data(
            repository.owner.username, repository.name
        ))
        
        # Prepare AI/ML analysis requests
        self.update_state(state='PROGRESS', meta={'step': 'preparing_analysis'})
        analysis_requests = prepare_analysis_requests(github_data, analysis_options)
        
        # Execute AI/ML analysis
        self.update_state(state='PROGRESS', meta={'step': 'running_analysis'})
        analysis_results = execute_aiml_analysis(analysis_requests)
        
        # Process and store results
        self.update_state(state='PROGRESS', meta={'step': 'storing_results'})
        insights = process_analysis_results(analysis_results, repository_id)
        
        # Save insights to database
        for insight_data in insights:
            insight = Insight(**insight_data)
            db.add(insight)
        
        # Update repository status
        repository.analysis_status = 'completed'
        repository.last_analyzed = datetime.utcnow()
        repository.analysis_completed_at = datetime.utcnow()
        db.commit()
        
        return {
            'status': 'completed',
            'repository_id': repository_id,
            'insights_count': len(insights),
            'analysis_duration': (
                repository.analysis_completed_at - repository.analysis_started_at
            ).total_seconds()
        }
        
    except Exception as exc:
        # Update repository status on failure
        try:
            repository.analysis_status = 'failed'
            repository.analysis_error = str(exc)
            db.commit()
        except:
            pass
        
        # Retry logic
        if self.request.retries < 3:
            raise self.retry(exc=exc, countdown=60 * (2 ** self.request.retries))
        else:
            raise exc
    
    finally:
        db.close()

def prepare_analysis_requests(github_data: Dict[str, Any], 
                            options: Dict[str, Any] = None) -> List[Dict[str, Any]]:
    """Prepare requests for AI/ML services."""
    requests = []
    options = options or {}
    
    # Code quality analysis
    if options.get('code_quality', True) and github_data.get('code_files'):
        requests.append({
            'service': 'code_quality',
            'endpoint': f"{settings.AIML_SERVICE_URL}/analyze/code-quality",
            'data': {
                'code_files': github_data['code_files'],
                'repository_metadata': github_data['metadata']
            }
        })
    
    # Sentiment analysis
    if options.get('sentiment_analysis', True):
        text_data = []
        text_data.extend(github_data.get('issues', []))
        text_data.extend(github_data.get('pull_requests', []))
        
        if text_data:
            requests.append({
                'service': 'sentiment_analysis',
                'endpoint': f"{settings.AIML_SERVICE_URL}/analyze/sentiment",
                'data': {
                    'texts': text_data,
                    'domain': 'github'
                }
            })
    
    # Trend prediction
    if options.get('trend_prediction', True) and github_data.get('time_series'):
        requests.append({
            'service': 'trend_prediction',
            'endpoint': f"{settings.AIML_SERVICE_URL}/predict/trends",
            'data': {
                'time_series': github_data['time_series'],
                'prediction_horizon': options.get('prediction_horizon', 30)
            }
        })
    
    return requests

def execute_aiml_analysis(requests: List[Dict[str, Any]]) -> Dict[str, Any]:
    """Execute AI/ML analysis requests."""
    results = {}
    
    for request in requests:
        try:
            response = requests.post(
                request['endpoint'],
                json=request['data'],
                headers={
                    'Authorization': f"Bearer {settings.AIML_SERVICE_TOKEN}",
                    'Content-Type': 'application/json'
                },
                timeout=300  # 5 minutes
            )
            
            if response.status_code == 200:
                results[request['service']] = response.json()
            else:
                logging.error(f"AI/ML service error for {request['service']}: {response.text}")
                results[request['service']] = {'error': response.text}
                
        except Exception as e:
            logging.error(f"Failed to call {request['service']}: {e}")
            results[request['service']] = {'error': str(e)}
    
    return results
```

### WebSocket Real-time Updates

```python
# backend/app/websocket/aiml_updates.py
from fastapi import WebSocket, WebSocketDisconnect
from typing import Dict, Any, List, Set
import json
import asyncio
import logging
from datetime import datetime

class AIMLWebSocketManager:
    """Manages WebSocket connections for AI/ML updates."""
    
    def __init__(self):
        self.active_connections: Dict[str, Set[WebSocket]] = {}
        self.connection_metadata: Dict[WebSocket, Dict[str, Any]] = {}
        self.logger = logging.getLogger(__name__)
    
    async def connect(self, websocket: WebSocket, repository_id: str, user_id: str):
        """Connect client to repository updates."""
        await websocket.accept()
        
        if repository_id not in self.active_connections:
            self.active_connections[repository_id] = set()
        
        self.active_connections[repository_id].add(websocket)
        self.connection_metadata[websocket] = {
            'repository_id': repository_id,
            'user_id': user_id,
            'connected_at': datetime.utcnow()
        }
        
        self.logger.info(f"WebSocket connected: user {user_id} to repository {repository_id}")
    
    def disconnect(self, websocket: WebSocket):
        """Disconnect client."""
        metadata = self.connection_metadata.get(websocket)
        if metadata:
            repository_id = metadata['repository_id']
            if repository_id in self.active_connections:
                self.active_connections[repository_id].discard(websocket)
                if not self.active_connections[repository_id]:
                    del self.active_connections[repository_id]
            
            del self.connection_metadata[websocket]
            self.logger.info(f"WebSocket disconnected: {metadata['user_id']}")
    
    async def send_analysis_update(self, repository_id: str, update: Dict[str, Any]):
        """Send analysis update to all connected clients."""
        if repository_id not in self.active_connections:
            return
        
        message = {
            'type': 'analysis_update',
            'repository_id': repository_id,
            'timestamp': datetime.utcnow().isoformat(),
            'data': update
        }
        
        # Send to all connected clients for this repository
        disconnected = set()
        for websocket in self.active_connections[repository_id]:
            try:
                await websocket.send_text(json.dumps(message))
            except Exception as e:
                self.logger.warning(f"Failed to send WebSocket message: {e}")
                disconnected.add(websocket)
        
        # Clean up disconnected clients
        for websocket in disconnected:
            self.disconnect(websocket)
    
    async def send_progress_update(self, repository_id: str, progress: Dict[str, Any]):
        """Send progress update for ongoing analysis."""
        await self.send_analysis_update(repository_id, {
            'type': 'progress',
            'progress': progress
        })
    
    async def send_completion_update(self, repository_id: str, results: Dict[str, Any]):
        """Send completion update with results."""
        await self.send_analysis_update(repository_id, {
            'type': 'completion',
            'results': results
        })
    
    async def send_error_update(self, repository_id: str, error: Dict[str, Any]):
        """Send error update."""
        await self.send_analysis_update(repository_id, {
            'type': 'error',
            'error': error
        })

# Global WebSocket manager instance
websocket_manager = AIMLWebSocketManager()

# WebSocket endpoint
@app.websocket("/ws/repository/{repository_id}/analysis")
async def websocket_analysis_updates(websocket: WebSocket, repository_id: str, 
                                   current_user: User = Depends(get_current_user_ws)):
    """WebSocket endpoint for real-time analysis updates."""
    await websocket_manager.connect(websocket, repository_id, current_user.id)
    
    try:
        while True:
            # Keep connection alive and handle client messages
            data = await websocket.receive_text()
            message = json.loads(data)
            
            # Handle client messages (e.g., pause/resume analysis)
            if message.get('type') == 'control':
                await handle_analysis_control(repository_id, message.get('action'))
            
    except WebSocketDisconnect:
        websocket_manager.disconnect(websocket)
    except Exception as e:
        logging.error(f"WebSocket error: {e}")
        websocket_manager.disconnect(websocket)
```

### Event-Driven Architecture

```python
# backend/app/events/aiml_events.py
from typing import Dict, Any, Callable, List
import asyncio
import json
from datetime import datetime
from dataclasses import dataclass, asdict
from enum import Enum

class EventType(Enum):
    ANALYSIS_STARTED = "analysis_started"
    ANALYSIS_PROGRESS = "analysis_progress"
    ANALYSIS_COMPLETED = "analysis_completed"
    ANALYSIS_FAILED = "analysis_failed"
    MODEL_UPDATED = "model_updated"
    INSIGHT_GENERATED = "insight_generated"

@dataclass
class AIMLEvent:
    """Base class for AI/ML events."""
    event_type: EventType
    repository_id: str
    timestamp: datetime
    data: Dict[str, Any]
    correlation_id: str = None
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'event_type': self.event_type.value,
            'repository_id': self.repository_id,
            'timestamp': self.timestamp.isoformat(),
            'data': self.data,
            'correlation_id': self.correlation_id
        }

class EventBus:
    """Event bus for AI/ML events."""
    
    def __init__(self):
        self.subscribers: Dict[EventType, List[Callable]] = {}
        self.event_history: List[AIMLEvent] = []
        self.max_history = 1000
    
    def subscribe(self, event_type: EventType, handler: Callable):
        """Subscribe to events of a specific type."""
        if event_type not in self.subscribers:
            self.subscribers[event_type] = []
        self.subscribers[event_type].append(handler)
    
    async def publish(self, event: AIMLEvent):
        """Publish an event to all subscribers."""
        # Store in history
        self.event_history.append(event)
        if len(self.event_history) > self.max_history:
            self.event_history.pop(0)
        
        # Notify subscribers
        if event.event_type in self.subscribers:
            tasks = []
            for handler in self.subscribers[event.event_type]:
                tasks.append(asyncio.create_task(handler(event)))
            
            if tasks:
                await asyncio.gather(*tasks, return_exceptions=True)
    
    def get_events(self, repository_id: str = None, 
                  event_type: EventType = None) -> List[AIMLEvent]:
        """Get events with optional filtering."""
        events = self.event_history
        
        if repository_id:
            events = [e for e in events if e.repository_id == repository_id]
        
        if event_type:
            events = [e for e in events if e.event_type == event_type]
        
        return events

# Global event bus
event_bus = EventBus()

# Event handlers
async def handle_analysis_started(event: AIMLEvent):
    """Handle analysis started event."""
    await websocket_manager.send_progress_update(
        event.repository_id,
        {
            'status': 'started',
            'message': 'Analysis has begun',
            'progress': 0
        }
    )

async def handle_analysis_progress(event: AIMLEvent):
    """Handle analysis progress event."""
    await websocket_manager.send_progress_update(
        event.repository_id,
        event.data
    )

async def handle_analysis_completed(event: AIMLEvent):
    """Handle analysis completed event."""
    await websocket_manager.send_completion_update(
        event.repository_id,
        event.data
    )
    
    # Trigger post-analysis workflows
    await trigger_post_analysis_workflows(event.repository_id, event.data)

async def handle_insight_generated(event: AIMLEvent):
    """Handle new insight generation."""
    # Send real-time insight to connected clients
    await websocket_manager.send_analysis_update(
        event.repository_id,
        {
            'type': 'new_insight',
            'insight': event.data
        }
    )
    
    # Trigger notifications if insight is high priority
    if event.data.get('priority') == 'high':
        await send_insight_notification(event.repository_id, event.data)

# Subscribe to events
event_bus.subscribe(EventType.ANALYSIS_STARTED, handle_analysis_started)
event_bus.subscribe(EventType.ANALYSIS_PROGRESS, handle_analysis_progress)
event_bus.subscribe(EventType.ANALYSIS_COMPLETED, handle_analysis_completed)
event_bus.subscribe(EventType.INSIGHT_GENERATED, handle_insight_generated)
```

## Error Handling and Resilience

### Circuit Breaker Pattern

```python
# backend/app/resilience/circuit_breaker.py
import time
import asyncio
from typing import Callable, Any, Optional
from enum import Enum
import logging

class CircuitState(Enum):
    CLOSED = "closed"
    OPEN = "open"
    HALF_OPEN = "half_open"

class CircuitBreaker:
    """Circuit breaker for AI/ML service calls."""
    
    def __init__(self, failure_threshold: int = 5, timeout: int = 60, 
                 expected_exception: type = Exception):
        self.failure_threshold = failure_threshold
        self.timeout = timeout
        self.expected_exception = expected_exception
        
        self.failure_count = 0
        self.last_failure_time = None
        self.state = CircuitState.CLOSED
        self.logger = logging.getLogger(__name__)
    
    async def call(self, func: Callable, *args, **kwargs) -> Any:
        """Execute function with circuit breaker protection."""
        
        if self.state == CircuitState.OPEN:
            if self._should_attempt_reset():
                self.state = CircuitState.HALF_OPEN
                self.logger.info("Circuit breaker: Attempting reset")
            else:
                raise Exception("Circuit breaker is OPEN")
        
        try:
            result = await func(*args, **kwargs)
            self._on_success()
            return result
            
        except self.expected_exception as e:
            self._on_failure()
            raise e
    
    def _should_attempt_reset(self) -> bool:
        """Check if enough time has passed to attempt reset."""
        return (time.time() - self.last_failure_time) >= self.timeout
    
    def _on_success(self):
        """Handle successful call."""
        self.failure_count = 0
        self.state = CircuitState.CLOSED
        self.logger.debug("Circuit breaker: Success, state reset to CLOSED")
    
    def _on_failure(self):
        """Handle failed call."""
        self.failure_count += 1
        self.last_failure_time = time.time()
        
        if self.failure_count >= self.failure_threshold:
            self.state = CircuitState.OPEN
            self.logger.warning(f"Circuit breaker: OPEN after {self.failure_count} failures")

# AI/ML service client with circuit breaker
class AIMLServiceClient:
    """Client for AI/ML services with resilience patterns."""
    
    def __init__(self, base_url: str):
        self.base_url = base_url
        self.circuit_breakers = {
            'code_quality': CircuitBreaker(failure_threshold=3, timeout=30),
            'sentiment_analysis': CircuitBreaker(failure_threshold=3, timeout=30),
            'trend_prediction': CircuitBreaker(failure_threshold=5, timeout=60)
        }
    
    async def analyze_code_quality(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze code quality with circuit breaker protection."""
        return await self.circuit_breakers['code_quality'].call(
            self._make_request,
            'POST',
            f"{self.base_url}/analyze/code-quality",
            data
        )
    
    async def _make_request(self, method: str, url: str, data: Dict[str, Any]) -> Dict[str, Any]:
        """Make HTTP request to AI/ML service."""
        import aiohttp
        
        async with aiohttp.ClientSession() as session:
            async with session.request(method, url, json=data) as response:
                if response.status == 200:
                    return await response.json()
                else:
                    raise Exception(f"AI/ML service error: {response.status}")
```

This comprehensive integration patterns documentation provides robust, scalable patterns for integrating AI/ML services with backend APIs, ensuring reliable communication, efficient processing, and excellent observability throughout the GitInsight system.
