# AI/ML Data Processing Pipelines Documentation

## Overview

The data processing pipeline is the foundation of GitInsight's AI/ML capabilities, transforming raw GitHub repository data into high-quality features suitable for machine learning models. This document covers the complete pipeline from data ingestion through feature extraction, validation, and preparation for model training and inference.

## Pipeline Architecture

### Data Flow Overview

```
┌─────────────────────────────────────────────────────────────────┐
│                    Data Processing Pipeline                     │
├─────────────────────────────────────────────────────────────────┤
│                                                                 │
│  ┌─────────────────────────────────────────────────────────────┐  │
│  │                   Raw Data Ingestion                        │  │
│  │                                                             │  │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────────────┐  │  │
│  │  │   GitHub    │  │   Webhook   │  │     Scheduled       │  │  │
│  │  │   API Pull  │  │   Events    │  │     Sync Jobs       │  │  │
│  │  │             │  │             │  │                     │  │  │
│  │  │ • REST API  │  │ • Push      │  │ • Daily Updates     │  │  │
│  │  │ • GraphQL   │  │ • Issues    │  │ • Weekly Full Sync  │  │  │
│  │  │ • Rate Limit│  │ • PRs       │  │ • Incremental       │  │  │
│  │  │ • Pagination│  │ • Releases  │  │ • Backfill          │  │  │
│  │  └─────────────┘  └─────────────┘  └─────────────────────┘  │  │
│  └─────────────────────────────────────────────────────────────┘  │
│           │                                                     │
│           ▼                                                     │
│  ┌─────────────────────────────────────────────────────────────┐  │
│  │                  Data Validation & Cleaning                 │  │
│  │                                                             │  │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────────────┐  │  │
│  │  │   Schema    │  │   Quality   │  │      Anomaly        │  │  │
│  │  │ Validation  │  │   Checks    │  │     Detection       │  │  │
│  │  │             │  │             │  │                     │  │  │
│  │  │ • Structure │  │ • Missing   │  │ • Statistical       │  │  │
│  │  │ • Types     │  │ • Duplicates│  │ • Pattern Based     │  │  │
│  │  │ • Required  │  │ • Outliers  │  │ • ML Based          │  │  │
│  │  │ • Formats   │  │ • Integrity │  │ • Domain Rules      │  │  │
│  │  └─────────────┘  └─────────────┘  └─────────────────────┘  │  │
│  └─────────────────────────────────────────────────────────────┘  │
│           │                                                     │
│           ▼                                                     │
│  ┌─────────────────────────────────────────────────────────────┐  │
│  │                   Feature Engineering                       │  │
│  │                                                             │  │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────────────┐  │  │
│  │  │    Code     │  │    Text     │  │     Temporal        │  │  │
│  │  │  Features   │  │  Features   │  │    Features         │  │  │
│  │  │             │  │             │  │                     │  │  │
│  │  │ • AST Parse │  │ • Tokenize  │  │ • Time Series       │  │  │
│  │  │ • Metrics   │  │ • Embeddings│  │ • Trends            │  │  │
│  │  │ • Patterns  │  │ • Sentiment │  │ • Seasonality       │  │  │
│  │  │ • Graph     │  │ • Topics    │  │ • Frequency         │  │  │
│  │  └─────────────┘  └─────────────┘  └─────────────────────┘  │  │
│  └─────────────────────────────────────────────────────────────┘  │
│           │                                                     │
│           ▼                                                     │
│  ┌─────────────────────────────────────────────────────────────┐  │
│  │                Feature Store & Serving                      │  │
│  │                                                             │  │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────────────┐  │  │
│  │  │   Feature   │  │   Batch     │  │     Real-time       │  │  │
│  │  │   Storage   │  │  Serving    │  │     Serving         │  │  │
│  │  │             │  │             │  │                     │  │  │
│  │  │ • Versioned │  │ • Training  │  │ • Online Features   │  │  │
│  │  │ • Indexed   │  │ • Batch     │  │ • Low Latency       │  │  │
│  │  │ • Partitioned│ │ • Historical│  │ • Streaming         │  │  │
│  │  │ • Compressed│  │ • Analytics │  │ • Cache Optimized   │  │  │
│  │  └─────────────┘  └─────────────┘  └─────────────────────┘  │  │
│  └─────────────────────────────────────────────────────────────┘  │
└─────────────────────────────────────────────────────────────────┘
```

## Data Ingestion Layer

### GitHub API Integration

```python
# aiml/ingestion/github_collector.py
import asyncio
import aiohttp
from typing import Dict, Any, List, Optional, AsyncGenerator
from dataclasses import dataclass
from datetime import datetime, timedelta
import logging

@dataclass
class IngestionConfig:
    api_token: str
    rate_limit_per_hour: int = 5000
    max_concurrent_requests: int = 10
    retry_attempts: int = 3
    backoff_factor: float = 2.0

class GitHubDataCollector:
    """Collects comprehensive data from GitHub repositories."""
    
    def __init__(self, config: IngestionConfig):
        self.config = config
        self.session: Optional[aiohttp.ClientSession] = None
        self.rate_limiter = RateLimiter(config.rate_limit_per_hour)
        self.logger = logging.getLogger(__name__)
    
    async def __aenter__(self):
        self.session = aiohttp.ClientSession(
            headers={
                'Authorization': f'token {self.config.api_token}',
                'Accept': 'application/vnd.github.v3+json',
                'User-Agent': 'GitInsight-AI-ML/1.0'
            },
            timeout=aiohttp.ClientTimeout(total=30)
        )
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
    
    async def collect_repository_data(self, owner: str, repo: str, 
                                    since: Optional[datetime] = None) -> Dict[str, Any]:
        """Collect comprehensive repository data."""
        
        # Define data collection tasks
        tasks = [
            self._collect_repository_metadata(owner, repo),
            self._collect_commits(owner, repo, since),
            self._collect_issues(owner, repo, since),
            self._collect_pull_requests(owner, repo, since),
            self._collect_releases(owner, repo),
            self._collect_contributors(owner, repo),
            self._collect_languages(owner, repo),
            self._collect_topics(owner, repo),
            self._collect_code_files(owner, repo)
        ]
        
        # Execute tasks concurrently with rate limiting
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Combine results
        repository_data = {
            'metadata': results[0] if not isinstance(results[0], Exception) else None,
            'commits': results[1] if not isinstance(results[1], Exception) else [],
            'issues': results[2] if not isinstance(results[2], Exception) else [],
            'pull_requests': results[3] if not isinstance(results[3], Exception) else [],
            'releases': results[4] if not isinstance(results[4], Exception) else [],
            'contributors': results[5] if not isinstance(results[5], Exception) else [],
            'languages': results[6] if not isinstance(results[6], Exception) else {},
            'topics': results[7] if not isinstance(results[7], Exception) else [],
            'code_files': results[8] if not isinstance(results[8], Exception) else [],
            'collected_at': datetime.utcnow().isoformat(),
            'collection_errors': [str(r) for r in results if isinstance(r, Exception)]
        }
        
        return repository_data
    
    async def _collect_commits(self, owner: str, repo: str, 
                             since: Optional[datetime] = None) -> List[Dict[str, Any]]:
        """Collect repository commits with detailed information."""
        commits = []
        page = 1
        per_page = 100
        
        params = {'per_page': per_page, 'page': page}
        if since:
            params['since'] = since.isoformat()
        
        while True:
            await self.rate_limiter.acquire()
            
            try:
                async with self.session.get(
                    f'https://api.github.com/repos/{owner}/{repo}/commits',
                    params=params
                ) as response:
                    if response.status == 200:
                        page_commits = await response.json()
                        if not page_commits:
                            break
                        
                        # Enrich commits with additional data
                        enriched_commits = await self._enrich_commits(
                            owner, repo, page_commits
                        )
                        commits.extend(enriched_commits)
                        
                        if len(page_commits) < per_page:
                            break
                        
                        page += 1
                        params['page'] = page
                    else:
                        self.logger.warning(f"Failed to fetch commits: {response.status}")
                        break
                        
            except Exception as e:
                self.logger.error(f"Error fetching commits: {e}")
                break
        
        return commits
    
    async def _enrich_commits(self, owner: str, repo: str, 
                            commits: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Enrich commits with additional metadata."""
        enriched = []
        
        for commit in commits:
            sha = commit['sha']
            
            # Get detailed commit information
            await self.rate_limiter.acquire()
            
            try:
                async with self.session.get(
                    f'https://api.github.com/repos/{owner}/{repo}/commits/{sha}'
                ) as response:
                    if response.status == 200:
                        detailed_commit = await response.json()
                        
                        # Extract relevant information
                        enriched_commit = {
                            'sha': sha,
                            'message': detailed_commit['commit']['message'],
                            'author': detailed_commit['commit']['author'],
                            'committer': detailed_commit['commit']['committer'],
                            'stats': detailed_commit.get('stats', {}),
                            'files': detailed_commit.get('files', []),
                            'parents': detailed_commit.get('parents', []),
                            'url': detailed_commit['html_url']
                        }
                        enriched.append(enriched_commit)
                    else:
                        # Fallback to basic commit info
                        enriched.append(commit)
                        
            except Exception as e:
                self.logger.warning(f"Failed to enrich commit {sha}: {e}")
                enriched.append(commit)
        
        return enriched
    
    async def _collect_code_files(self, owner: str, repo: str, 
                                max_files: int = 1000) -> List[Dict[str, Any]]:
        """Collect code files for analysis."""
        code_files = []
        
        # Get repository tree
        await self.rate_limiter.acquire()
        
        try:
            async with self.session.get(
                f'https://api.github.com/repos/{owner}/{repo}/git/trees/HEAD',
                params={'recursive': 1}
            ) as response:
                if response.status == 200:
                    tree_data = await response.json()
                    
                    # Filter for code files
                    code_extensions = {
                        '.py', '.js', '.ts', '.java', '.cpp', '.c', '.cs', 
                        '.go', '.rs', '.rb', '.php', '.swift', '.kt', '.scala'
                    }
                    
                    for item in tree_data.get('tree', []):
                        if (item['type'] == 'blob' and 
                            any(item['path'].endswith(ext) for ext in code_extensions)):
                            
                            if len(code_files) >= max_files:
                                break
                            
                            # Get file content
                            file_content = await self._get_file_content(
                                owner, repo, item['path']
                            )
                            
                            if file_content:
                                code_files.append({
                                    'path': item['path'],
                                    'content': file_content,
                                    'size': item['size'],
                                    'sha': item['sha']
                                })
                
        except Exception as e:
            self.logger.error(f"Error collecting code files: {e}")
        
        return code_files
```

## Data Validation and Cleaning

### Schema Validation

```python
# aiml/validation/schema_validator.py
from typing import Dict, Any, List, Optional
from pydantic import BaseModel, validator, Field
from datetime import datetime
import re

class CommitSchema(BaseModel):
    """Schema for commit data validation."""
    sha: str = Field(..., regex=r'^[a-f0-9]{40}$')
    message: str = Field(..., min_length=1, max_length=10000)
    author: Dict[str, Any]
    committer: Dict[str, Any]
    stats: Optional[Dict[str, int]] = None
    files: List[Dict[str, Any]] = []
    
    @validator('author', 'committer')
    def validate_person(cls, v):
        required_fields = {'name', 'email', 'date'}
        if not all(field in v for field in required_fields):
            raise ValueError(f"Missing required fields: {required_fields - set(v.keys())}")
        
        # Validate email format
        email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        if not re.match(email_pattern, v['email']):
            raise ValueError(f"Invalid email format: {v['email']}")
        
        return v
    
    @validator('stats')
    def validate_stats(cls, v):
        if v is not None:
            required_stats = {'additions', 'deletions', 'total'}
            if not all(stat in v for stat in required_stats):
                # Set default values for missing stats
                v.setdefault('additions', 0)
                v.setdefault('deletions', 0)
                v.setdefault('total', v.get('additions', 0) + v.get('deletions', 0))
        return v

class RepositoryDataSchema(BaseModel):
    """Schema for complete repository data validation."""
    metadata: Dict[str, Any]
    commits: List[CommitSchema] = []
    issues: List[Dict[str, Any]] = []
    pull_requests: List[Dict[str, Any]] = []
    releases: List[Dict[str, Any]] = []
    contributors: List[Dict[str, Any]] = []
    languages: Dict[str, int] = {}
    topics: List[str] = []
    code_files: List[Dict[str, Any]] = []
    collected_at: datetime
    
    @validator('metadata')
    def validate_metadata(cls, v):
        required_fields = {'id', 'name', 'full_name', 'owner', 'created_at'}
        missing_fields = required_fields - set(v.keys())
        if missing_fields:
            raise ValueError(f"Missing metadata fields: {missing_fields}")
        return v

class DataValidator:
    """Validates and cleans repository data."""
    
    def __init__(self):
        self.validation_errors = []
        self.cleaning_stats = {}
    
    async def validate_and_clean(self, raw_data: Dict[str, Any]) -> Dict[str, Any]:
        """Validate and clean repository data."""
        self.validation_errors = []
        self.cleaning_stats = {
            'records_processed': 0,
            'records_cleaned': 0,
            'records_removed': 0,
            'fields_corrected': 0
        }
        
        try:
            # Validate overall schema
            validated_data = RepositoryDataSchema(**raw_data)
            
            # Clean individual components
            cleaned_data = {
                'metadata': self._clean_metadata(validated_data.metadata),
                'commits': await self._clean_commits(validated_data.commits),
                'issues': self._clean_issues(validated_data.issues),
                'pull_requests': self._clean_pull_requests(validated_data.pull_requests),
                'releases': self._clean_releases(validated_data.releases),
                'contributors': self._clean_contributors(validated_data.contributors),
                'languages': self._clean_languages(validated_data.languages),
                'topics': self._clean_topics(validated_data.topics),
                'code_files': await self._clean_code_files(validated_data.code_files),
                'collected_at': validated_data.collected_at,
                'validation_summary': {
                    'errors': self.validation_errors,
                    'cleaning_stats': self.cleaning_stats
                }
            }
            
            return cleaned_data
            
        except Exception as e:
            self.validation_errors.append(f"Schema validation failed: {str(e)}")
            raise ValueError(f"Data validation failed: {self.validation_errors}")
    
    async def _clean_commits(self, commits: List[CommitSchema]) -> List[Dict[str, Any]]:
        """Clean and normalize commit data."""
        cleaned_commits = []
        
        for commit in commits:
            self.cleaning_stats['records_processed'] += 1
            
            try:
                cleaned_commit = commit.dict()
                
                # Normalize commit message
                cleaned_commit['message'] = self._normalize_text(commit.message)
                
                # Extract commit patterns
                cleaned_commit['patterns'] = self._extract_commit_patterns(commit.message)
                
                # Calculate derived metrics
                cleaned_commit['metrics'] = self._calculate_commit_metrics(commit)
                
                cleaned_commits.append(cleaned_commit)
                self.cleaning_stats['records_cleaned'] += 1
                
            except Exception as e:
                self.validation_errors.append(f"Failed to clean commit {commit.sha}: {str(e)}")
                self.cleaning_stats['records_removed'] += 1
        
        return cleaned_commits
    
    def _normalize_text(self, text: str) -> str:
        """Normalize text content."""
        if not text:
            return ""
        
        # Remove excessive whitespace
        normalized = re.sub(r'\s+', ' ', text.strip())
        
        # Remove control characters
        normalized = re.sub(r'[\x00-\x1f\x7f-\x9f]', '', normalized)
        
        return normalized
    
    def _extract_commit_patterns(self, message: str) -> Dict[str, Any]:
        """Extract patterns from commit messages."""
        patterns = {
            'type': None,
            'scope': None,
            'breaking_change': False,
            'issue_references': [],
            'co_authors': []
        }
        
        # Conventional commit pattern
        conventional_pattern = r'^(\w+)(\(.+\))?\!?:\s*(.+)'
        match = re.match(conventional_pattern, message)
        if match:
            patterns['type'] = match.group(1)
            patterns['scope'] = match.group(2).strip('()') if match.group(2) else None
            patterns['breaking_change'] = '!' in match.group(0)
        
        # Issue references
        issue_pattern = r'#(\d+)'
        patterns['issue_references'] = re.findall(issue_pattern, message)
        
        # Co-authors
        coauthor_pattern = r'Co-authored-by:\s*(.+)\s*<(.+)>'
        patterns['co_authors'] = re.findall(coauthor_pattern, message)
        
        return patterns
```

## Feature Engineering

### Code Feature Extraction

```python
# aiml/features/code_features.py
import ast
import tokenize
from typing import Dict, Any, List, Optional
from io import StringIO
import re
from collections import Counter, defaultdict

class CodeFeatureExtractor:
    """Extracts features from source code."""
    
    def __init__(self):
        self.supported_languages = {
            '.py': self._extract_python_features,
            '.js': self._extract_javascript_features,
            '.ts': self._extract_typescript_features,
            '.java': self._extract_java_features
        }
    
    async def extract_features(self, code_files: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Extract comprehensive code features."""
        features = {
            'file_count': len(code_files),
            'total_lines': 0,
            'total_characters': 0,
            'language_distribution': Counter(),
            'complexity_metrics': {},
            'quality_metrics': {},
            'pattern_metrics': {},
            'dependency_metrics': {}
        }
        
        for file_data in code_files:
            file_features = await self._extract_file_features(file_data)
            self._aggregate_features(features, file_features)
        
        # Calculate derived metrics
        features['avg_file_size'] = (
            features['total_lines'] / features['file_count'] 
            if features['file_count'] > 0 else 0
        )
        
        features['code_density'] = self._calculate_code_density(features)
        features['maintainability_index'] = self._calculate_maintainability_index(features)
        
        return features
    
    async def _extract_file_features(self, file_data: Dict[str, Any]) -> Dict[str, Any]:
        """Extract features from a single file."""
        path = file_data['path']
        content = file_data['content']
        
        # Basic metrics
        lines = content.split('\n')
        features = {
            'path': path,
            'lines_of_code': len(lines),
            'characters': len(content),
            'extension': self._get_file_extension(path)
        }
        
        # Language-specific features
        extension = features['extension']
        if extension in self.supported_languages:
            lang_features = await self.supported_languages[extension](content)
            features.update(lang_features)
        else:
            # Generic text-based features
            features.update(self._extract_generic_features(content))
        
        return features
    
    async def _extract_python_features(self, content: str) -> Dict[str, Any]:
        """Extract Python-specific features."""
        features = {
            'language': 'python',
            'functions': 0,
            'classes': 0,
            'imports': 0,
            'complexity': 0,
            'docstrings': 0,
            'comments': 0,
            'blank_lines': 0
        }
        
        try:
            # Parse AST
            tree = ast.parse(content)
            
            # Count different node types
            for node in ast.walk(tree):
                if isinstance(node, ast.FunctionDef):
                    features['functions'] += 1
                    features['complexity'] += self._calculate_cyclomatic_complexity(node)
                elif isinstance(node, ast.ClassDef):
                    features['classes'] += 1
                elif isinstance(node, (ast.Import, ast.ImportFrom)):
                    features['imports'] += 1
            
            # Count docstrings and comments
            features['docstrings'] = self._count_docstrings(tree)
            features['comments'] = self._count_comments(content)
            features['blank_lines'] = self._count_blank_lines(content)
            
            # Calculate additional metrics
            features['lines_per_function'] = (
                features['functions'] / max(features['functions'], 1)
            )
            features['comment_ratio'] = (
                features['comments'] / len(content.split('\n'))
            )
            
        except SyntaxError:
            # Handle syntax errors gracefully
            features.update(self._extract_generic_features(content))
        
        return features
    
    def _calculate_cyclomatic_complexity(self, node: ast.AST) -> int:
        """Calculate cyclomatic complexity for a function."""
        complexity = 1  # Base complexity
        
        for child in ast.walk(node):
            if isinstance(child, (ast.If, ast.While, ast.For, ast.AsyncFor)):
                complexity += 1
            elif isinstance(child, ast.ExceptHandler):
                complexity += 1
            elif isinstance(child, (ast.And, ast.Or)):
                complexity += 1
        
        return complexity
    
    def _count_docstrings(self, tree: ast.AST) -> int:
        """Count docstrings in the AST."""
        docstring_count = 0
        
        for node in ast.walk(tree):
            if isinstance(node, (ast.FunctionDef, ast.ClassDef, ast.Module)):
                if (node.body and 
                    isinstance(node.body[0], ast.Expr) and
                    isinstance(node.body[0].value, ast.Str)):
                    docstring_count += 1
        
        return docstring_count
```

### Text Feature Extraction

```python
# aiml/features/text_features.py
import spacy
import re
from typing import Dict, Any, List
from collections import Counter
from textstat import flesch_reading_ease, flesch_kincaid_grade
from vaderSentiment.vaderSentiment import SentimentIntensityAnalyzer

class TextFeatureExtractor:
    """Extracts features from text content (issues, PRs, comments)."""
    
    def __init__(self):
        self.nlp = spacy.load('en_core_web_sm')
        self.sentiment_analyzer = SentimentIntensityAnalyzer()
        
        # Technical vocabulary for software development
        self.tech_keywords = {
            'bug', 'fix', 'feature', 'enhancement', 'refactor', 'optimize',
            'performance', 'security', 'test', 'documentation', 'api',
            'database', 'frontend', 'backend', 'ui', 'ux', 'deploy'
        }
    
    async def extract_features(self, issues: List[Dict[str, Any]], 
                             pull_requests: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Extract comprehensive text features."""
        
        # Combine all text content
        all_texts = []
        issue_texts = []
        pr_texts = []
        
        for issue in issues:
            text = f"{issue.get('title', '')} {issue.get('body', '')}"
            all_texts.append(text)
            issue_texts.append(text)
        
        for pr in pull_requests:
            text = f"{pr.get('title', '')} {pr.get('body', '')}"
            all_texts.append(text)
            pr_texts.append(text)
        
        # Extract features
        features = {
            'total_texts': len(all_texts),
            'issue_count': len(issues),
            'pr_count': len(pull_requests),
            'sentiment_analysis': await self._analyze_sentiment(all_texts),
            'readability_metrics': self._calculate_readability(all_texts),
            'linguistic_features': await self._extract_linguistic_features(all_texts),
            'topic_analysis': await self._analyze_topics(all_texts),
            'technical_vocabulary': self._analyze_technical_vocabulary(all_texts),
            'issue_patterns': self._analyze_issue_patterns(issue_texts),
            'pr_patterns': self._analyze_pr_patterns(pr_texts)
        }
        
        return features
    
    async def _analyze_sentiment(self, texts: List[str]) -> Dict[str, Any]:
        """Analyze sentiment of text content."""
        if not texts:
            return {'overall_sentiment': 0.0, 'sentiment_distribution': {}}
        
        sentiments = []
        sentiment_scores = {'positive': 0, 'neutral': 0, 'negative': 0}
        
        for text in texts:
            if text.strip():
                scores = self.sentiment_analyzer.polarity_scores(text)
                sentiments.append(scores['compound'])
                
                # Categorize sentiment
                if scores['compound'] >= 0.05:
                    sentiment_scores['positive'] += 1
                elif scores['compound'] <= -0.05:
                    sentiment_scores['negative'] += 1
                else:
                    sentiment_scores['neutral'] += 1
        
        return {
            'overall_sentiment': sum(sentiments) / len(sentiments) if sentiments else 0.0,
            'sentiment_distribution': sentiment_scores,
            'sentiment_variance': np.var(sentiments) if sentiments else 0.0,
            'sentiment_trend': self._calculate_sentiment_trend(sentiments)
        }
    
    async def _extract_linguistic_features(self, texts: List[str]) -> Dict[str, Any]:
        """Extract linguistic features using spaCy."""
        if not texts:
            return {}
        
        # Combine texts for analysis
        combined_text = ' '.join(texts)
        doc = self.nlp(combined_text)
        
        # Extract features
        features = {
            'total_tokens': len(doc),
            'unique_tokens': len(set(token.text.lower() for token in doc)),
            'avg_sentence_length': np.mean([len(sent) for sent in doc.sents]),
            'pos_distribution': Counter(token.pos_ for token in doc),
            'named_entities': Counter(ent.label_ for ent in doc.ents),
            'dependency_patterns': Counter(token.dep_ for token in doc),
            'lexical_diversity': len(set(token.lemma_.lower() for token in doc)) / len(doc)
        }
        
        return features
    
    def _analyze_technical_vocabulary(self, texts: List[str]) -> Dict[str, Any]:
        """Analyze usage of technical vocabulary."""
        if not texts:
            return {}
        
        combined_text = ' '.join(texts).lower()
        words = re.findall(r'\b\w+\b', combined_text)
        
        tech_word_count = sum(1 for word in words if word in self.tech_keywords)
        total_words = len(words)
        
        return {
            'technical_density': tech_word_count / total_words if total_words > 0 else 0,
            'technical_words_used': [word for word in self.tech_keywords if word in combined_text],
            'domain_specific_terms': self._extract_domain_terms(words)
        }
```

This comprehensive data processing pipeline documentation provides the foundation for transforming raw GitHub data into high-quality features suitable for machine learning models, ensuring data quality, consistency, and optimal performance for AI/ML inference.
