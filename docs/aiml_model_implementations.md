# AI/ML Model Implementations Documentation

## Overview

This document provides detailed implementations of the core AI/ML models used in GitInsight for analyzing GitHub repositories. These models include code quality analysis, sentiment analysis, trend prediction, security analysis, and pattern detection. Each model is designed to extract meaningful insights from different aspects of repository data.

## Model Architecture Overview

### Model Ecosystem

```
┌─────────────────────────────────────────────────────────────────┐
│                    GitInsight AI/ML Models                      │
├─────────────────────────────────────────────────────────────────┤
│                                                                 │
│  ┌─────────────────────────────────────────────────────────────┐  │
│  │                   Code Analysis Models                       │  │
│  │                                                             │  │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────────────┐  │  │
│  │  │   Quality   │  │  Security   │  │     Complexity      │  │  │
│  │  │  Analyzer   │  │  Scanner    │  │     Detector        │  │  │
│  │  │             │  │             │  │                     │  │  │
│  │  │ • CodeBERT  │  │ • AST Parse │  │ • Cyclomatic        │  │  │
│  │  │ • Metrics   │  │ • Patterns  │  │ • Cognitive         │  │  │
│  │  │ • Rules     │  │ • CVE Match │  │ • Halstead          │  │  │
│  │  │ • ML Score  │  │ • SAST      │  │ • Maintainability   │  │  │
│  │  └─────────────┘  └─────────────┘  └─────────────────────┘  │  │
│  └─────────────────────────────────────────────────────────────┘  │
│           │                                                     │
│           ▼                                                     │
│  ┌─────────────────────────────────────────────────────────────┐  │
│  │                   Text Analysis Models                      │  │
│  │                                                             │  │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────────────┐  │  │
│  │  │  Sentiment  │  │    Topic    │  │      Intent         │  │  │
│  │  │  Analysis   │  │  Modeling   │  │   Classification    │  │  │
│  │  │             │  │             │  │                     │  │  │
│  │  │ • BERT      │  │ • LDA       │  │ • Issue Types       │  │  │
│  │  │ • RoBERTa   │  │ • BERTopic  │  │ • PR Categories     │  │  │
│  │  │ • VADER     │  │ • Clustering│  │ • Bug Reports       │  │  │
│  │  │ • Custom    │  │ • Keywords  │  │ • Feature Requests  │  │  │
│  │  └─────────────┘  └─────────────┘  └─────────────────────┘  │  │
│  └─────────────────────────────────────────────────────────────┘  │
│           │                                                     │
│           ▼                                                     │
│  ┌─────────────────────────────────────────────────────────────┐  │
│  │                 Temporal Analysis Models                    │  │
│  │                                                             │  │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────────────┐  │  │
│  │  │    Trend    │  │ Seasonality │  │     Anomaly         │  │  │
│  │  │ Prediction  │  │  Detection  │  │    Detection        │  │  │
│  │  │             │  │             │  │                     │  │  │
│  │  │ • LSTM/GRU  │  │ • STL       │  │ • Isolation Forest  │  │  │
│  │  │ • Prophet   │  │ • Fourier   │  │ • One-Class SVM     │  │  │
│  │  │ • ARIMA     │  │ • Wavelet   │  │ • Autoencoders      │  │  │
│  │  │ • Transformer│ │ • Cyclical  │  │ • Statistical       │  │  │
│  │  └─────────────┘  └─────────────┘  └─────────────────────┘  │  │
│  └─────────────────────────────────────────────────────────────┘  │
│           │                                                     │
│           ▼                                                     │
│  ┌─────────────────────────────────────────────────────────────┐  │
│  │                 Ensemble & Meta Models                      │  │
│  │                                                             │  │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────────────┐  │  │
│  │  │   Insight   │  │ Confidence  │  │     Ranking         │  │  │
│  │  │ Synthesis   │  │ Estimation  │  │     Models          │  │  │
│  │  │             │  │             │  │                     │  │  │
│  │  │ • Voting    │  │ • Calibration│ │ • Learning to Rank  │  │  │
│  │  │ • Stacking  │  │ • Uncertainty│ │ • Pairwise          │  │  │
│  │  │ • Blending  │  │ • Bayesian  │  │ • Listwise          │  │  │
│  │  │ • Weighted  │  │ • Bootstrap │  │ • Feature Based     │  │  │
│  │  └─────────────┘  └─────────────┘  └─────────────────────┘  │  │
│  └─────────────────────────────────────────────────────────────┘  │
└─────────────────────────────────────────────────────────────────┘
```

## Code Quality Analysis Model

### Multi-Modal Code Quality Analyzer

```python
# aiml/models/code_quality.py
import torch
import torch.nn as nn
import torch.nn.functional as F
from transformers import AutoModel, AutoTokenizer
from typing import Dict, Any, List, Tuple, Optional
import numpy as np

class CodeQualityAnalyzer(nn.Module):
    """Multi-modal neural network for comprehensive code quality assessment."""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__()
        self.config = config
        
        # Code encoder using pre-trained code model
        self.code_encoder = AutoModel.from_pretrained(
            config.get('code_model', 'microsoft/codebert-base')
        )
        
        # Freeze early layers if specified
        if config.get('freeze_encoder_layers', 0) > 0:
            for param in list(self.code_encoder.parameters())[:config['freeze_encoder_layers']]:
                param.requires_grad = False
        
        # Metric features encoder
        metric_dim = config.get('metric_features', 20)
        self.metric_encoder = nn.Sequential(
            nn.Linear(metric_dim, 256),
            nn.BatchNorm1d(256),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(256, 128),
            nn.BatchNorm1d(128),
            nn.ReLU(),
            nn.Dropout(0.1)
        )
        
        # AST features encoder
        ast_dim = config.get('ast_features', 50)
        self.ast_encoder = nn.Sequential(
            nn.Linear(ast_dim, 128),
            nn.BatchNorm1d(128),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(128, 64),
            nn.BatchNorm1d(64),
            nn.ReLU()
        )
        
        # Attention mechanism for feature fusion
        code_dim = self.code_encoder.config.hidden_size
        total_dim = code_dim + 128 + 64  # code + metrics + ast
        
        self.attention = nn.MultiheadAttention(
            embed_dim=total_dim,
            num_heads=8,
            dropout=0.1,
            batch_first=True
        )
        
        # Quality classification heads
        self.quality_classifier = nn.Sequential(
            nn.Linear(total_dim, 512),
            nn.BatchNorm1d(512),
            nn.ReLU(),
            nn.Dropout(0.3),
            nn.Linear(512, 256),
            nn.BatchNorm1d(256),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(256, config.get('num_quality_classes', 6))
        )
        
        # Overall quality score regression
        self.quality_regressor = nn.Sequential(
            nn.Linear(total_dim, 256),
            nn.BatchNorm1d(256),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(256, 128),
            nn.BatchNorm1d(128),
            nn.ReLU(),
            nn.Linear(128, 1),
            nn.Sigmoid()
        )
        
        # Maintainability index predictor
        self.maintainability_predictor = nn.Sequential(
            nn.Linear(total_dim, 128),
            nn.ReLU(),
            nn.Linear(128, 1)
        )
        
        # Technical debt estimator
        self.debt_estimator = nn.Sequential(
            nn.Linear(total_dim, 128),
            nn.ReLU(),
            nn.Linear(128, 1),
            nn.ReLU()  # Ensure positive debt estimation
        )
    
    def forward(self, code_tokens: torch.Tensor, attention_mask: torch.Tensor,
                metric_features: torch.Tensor, ast_features: torch.Tensor) -> Dict[str, torch.Tensor]:
        """Forward pass for code quality analysis."""
        batch_size = code_tokens.size(0)
        
        # Encode code using pre-trained model
        code_outputs = self.code_encoder(
            input_ids=code_tokens,
            attention_mask=attention_mask
        )
        
        # Pool code representations (mean pooling with attention mask)
        code_embeddings = self._masked_mean_pooling(
            code_outputs.last_hidden_state, attention_mask
        )
        
        # Encode metric features
        metric_embeddings = self.metric_encoder(metric_features)
        
        # Encode AST features
        ast_embeddings = self.ast_encoder(ast_features)
        
        # Concatenate all features
        combined_features = torch.cat([
            code_embeddings, metric_embeddings, ast_embeddings
        ], dim=1)
        
        # Apply self-attention for feature refinement
        combined_features_expanded = combined_features.unsqueeze(1)
        attended_features, _ = self.attention(
            combined_features_expanded,
            combined_features_expanded,
            combined_features_expanded
        )
        attended_features = attended_features.squeeze(1)
        
        # Generate predictions
        quality_classes = self.quality_classifier(attended_features)
        quality_score = self.quality_regressor(attended_features)
        maintainability = self.maintainability_predictor(attended_features)
        technical_debt = self.debt_estimator(attended_features)
        
        return {
            'quality_classes': quality_classes,
            'quality_score': quality_score,
            'maintainability_index': maintainability,
            'technical_debt': technical_debt,
            'embeddings': attended_features
        }
    
    def _masked_mean_pooling(self, hidden_states: torch.Tensor, 
                           attention_mask: torch.Tensor) -> torch.Tensor:
        """Apply mean pooling with attention mask."""
        # Expand attention mask to match hidden states dimensions
        attention_mask_expanded = attention_mask.unsqueeze(-1).expand(hidden_states.size())
        
        # Apply mask and compute mean
        masked_hidden = hidden_states * attention_mask_expanded
        sum_hidden = torch.sum(masked_hidden, dim=1)
        sum_mask = torch.sum(attention_mask_expanded, dim=1)
        
        # Avoid division by zero
        mean_hidden = sum_hidden / torch.clamp(sum_mask, min=1e-9)
        
        return mean_hidden

class CodeQualityService:
    """Service for code quality analysis using the trained model."""
    
    def __init__(self, model_path: str, device: str = 'auto'):
        self.device = self._get_device(device)
        self.model = self._load_model(model_path)
        self.tokenizer = AutoTokenizer.from_pretrained(model_path)
        
        # Quality categories mapping
        self.quality_categories = [
            'maintainability', 'readability', 'complexity',
            'documentation', 'testing', 'security'
        ]
        
        # Metric extractors
        self.metric_extractor = CodeMetricExtractor()
        self.ast_extractor = ASTFeatureExtractor()
    
    def _get_device(self, device: str) -> torch.device:
        """Get appropriate device for inference."""
        if device == 'auto':
            return torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        return torch.device(device)
    
    def _load_model(self, model_path: str) -> CodeQualityAnalyzer:
        """Load trained model."""
        checkpoint = torch.load(f"{model_path}/model.pt", map_location=self.device)
        
        # Load configuration
        config = checkpoint.get('config', {})
        
        # Initialize model
        model = CodeQualityAnalyzer(config)
        model.load_state_dict(checkpoint['model_state_dict'])
        model.to(self.device)
        model.eval()
        
        return model
    
    async def analyze_code_quality(self, code_files: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Analyze code quality for a collection of files."""
        if not code_files:
            return self._empty_analysis_result()
        
        # Prepare input data
        combined_code = self._combine_code_files(code_files)
        
        # Extract features
        metric_features = await self.metric_extractor.extract_features(code_files)
        ast_features = await self.ast_extractor.extract_features(code_files)
        
        # Tokenize code
        tokens = self.tokenizer(
            combined_code,
            max_length=512,
            truncation=True,
            padding=True,
            return_tensors='pt'
        ).to(self.device)
        
        # Prepare feature tensors
        metric_tensor = torch.tensor([metric_features], dtype=torch.float32).to(self.device)
        ast_tensor = torch.tensor([ast_features], dtype=torch.float32).to(self.device)
        
        # Run inference
        with torch.no_grad():
            outputs = self.model(
                code_tokens=tokens['input_ids'],
                attention_mask=tokens['attention_mask'],
                metric_features=metric_tensor,
                ast_features=ast_tensor
            )
        
        # Process results
        return self._process_outputs(outputs, code_files)
    
    def _process_outputs(self, outputs: Dict[str, torch.Tensor], 
                        code_files: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Process model outputs into analysis results."""
        # Extract predictions
        quality_probs = torch.softmax(outputs['quality_classes'], dim=1).cpu().numpy()[0]
        quality_score = outputs['quality_score'].cpu().item()
        maintainability = outputs['maintainability_index'].cpu().item()
        technical_debt = outputs['technical_debt'].cpu().item()
        
        # Calculate file-level metrics
        file_metrics = self._calculate_file_metrics(code_files)
        
        # Generate insights
        insights = self._generate_insights(
            quality_score, quality_probs, maintainability, technical_debt, file_metrics
        )
        
        # Generate recommendations
        recommendations = self._generate_recommendations(
            quality_score, quality_probs, file_metrics
        )
        
        return {
            'overall_quality_score': quality_score,
            'maintainability_index': maintainability,
            'technical_debt_hours': technical_debt,
            'quality_categories': dict(zip(self.quality_categories, quality_probs)),
            'file_metrics': file_metrics,
            'insights': insights,
            'recommendations': recommendations,
            'confidence': self._calculate_confidence(outputs)
        }
    
    def _generate_insights(self, quality_score: float, quality_probs: np.ndarray,
                          maintainability: float, technical_debt: float,
                          file_metrics: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Generate actionable insights from analysis results."""
        insights = []
        
        # Overall quality insight
        if quality_score >= 0.8:
            insights.append({
                'type': 'positive',
                'category': 'overall',
                'title': 'High Code Quality',
                'description': f'The codebase demonstrates excellent quality with a score of {quality_score:.2f}',
                'priority': 'low'
            })
        elif quality_score <= 0.4:
            insights.append({
                'type': 'warning',
                'category': 'overall',
                'title': 'Low Code Quality',
                'description': f'The codebase needs significant improvement (score: {quality_score:.2f})',
                'priority': 'high'
            })
        
        # Category-specific insights
        for i, category in enumerate(self.quality_categories):
            prob = quality_probs[i]
            if prob < 0.3:
                insights.append({
                    'type': 'warning',
                    'category': category,
                    'title': f'Poor {category.title()}',
                    'description': f'{category.title()} needs attention (score: {prob:.2f})',
                    'priority': 'medium' if prob > 0.2 else 'high'
                })
        
        # Technical debt insight
        if technical_debt > 40:
            insights.append({
                'type': 'warning',
                'category': 'technical_debt',
                'title': 'High Technical Debt',
                'description': f'Estimated {technical_debt:.1f} hours of technical debt',
                'priority': 'high' if technical_debt > 80 else 'medium'
            })
        
        return insights
    
    def _generate_recommendations(self, quality_score: float, quality_probs: np.ndarray,
                                file_metrics: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Generate actionable recommendations."""
        recommendations = []
        
        # Complexity recommendations
        if file_metrics.get('avg_complexity', 0) > 10:
            recommendations.append({
                'category': 'complexity',
                'title': 'Reduce Cyclomatic Complexity',
                'description': 'Break down complex functions into smaller, more manageable pieces',
                'impact': 'high',
                'effort': 'medium'
            })
        
        # Documentation recommendations
        if quality_probs[3] < 0.4:  # documentation category
            recommendations.append({
                'category': 'documentation',
                'title': 'Improve Code Documentation',
                'description': 'Add docstrings and comments to improve code understanding',
                'impact': 'medium',
                'effort': 'low'
            })
        
        # Testing recommendations
        if quality_probs[4] < 0.5:  # testing category
            recommendations.append({
                'category': 'testing',
                'title': 'Increase Test Coverage',
                'description': 'Add unit tests to improve code reliability',
                'impact': 'high',
                'effort': 'high'
            })
        
        return recommendations
```

## Sentiment Analysis Model

### Multi-Domain Sentiment Analyzer

```python
# aiml/models/sentiment_analysis.py
import torch
import torch.nn as nn
from transformers import AutoModel, AutoTokenizer
from typing import Dict, Any, List, Tuple
import numpy as np

class SentimentAnalyzer(nn.Module):
    """Multi-domain sentiment analysis for GitHub text content."""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__()
        self.config = config
        
        # Base transformer model
        self.transformer = AutoModel.from_pretrained(
            config.get('base_model', 'roberta-base')
        )
        
        # Domain-specific adapters
        hidden_size = self.transformer.config.hidden_size
        self.domain_adapters = nn.ModuleDict({
            'issues': self._create_adapter(hidden_size),
            'pull_requests': self._create_adapter(hidden_size),
            'commits': self._create_adapter(hidden_size),
            'reviews': self._create_adapter(hidden_size)
        })
        
        # Sentiment classification head
        self.sentiment_classifier = nn.Sequential(
            nn.Linear(hidden_size, 256),
            nn.ReLU(),
            nn.Dropout(0.3),
            nn.Linear(256, 128),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(128, 3)  # negative, neutral, positive
        )
        
        # Emotion classification head
        self.emotion_classifier = nn.Sequential(
            nn.Linear(hidden_size, 256),
            nn.ReLU(),
            nn.Dropout(0.3),
            nn.Linear(256, config.get('num_emotions', 8))
        )
        
        # Intensity regression head
        self.intensity_regressor = nn.Sequential(
            nn.Linear(hidden_size, 128),
            nn.ReLU(),
            nn.Linear(128, 1),
            nn.Sigmoid()
        )
    
    def _create_adapter(self, hidden_size: int) -> nn.Module:
        """Create domain-specific adapter layer."""
        return nn.Sequential(
            nn.Linear(hidden_size, hidden_size // 4),
            nn.ReLU(),
            nn.Linear(hidden_size // 4, hidden_size),
            nn.LayerNorm(hidden_size)
        )
    
    def forward(self, input_ids: torch.Tensor, attention_mask: torch.Tensor,
                domain: str = 'issues') -> Dict[str, torch.Tensor]:
        """Forward pass for sentiment analysis."""
        
        # Get transformer outputs
        outputs = self.transformer(
            input_ids=input_ids,
            attention_mask=attention_mask
        )
        
        # Pool sequence representation
        pooled_output = self._mean_pooling(outputs.last_hidden_state, attention_mask)
        
        # Apply domain-specific adapter
        if domain in self.domain_adapters:
            adapted_output = self.domain_adapters[domain](pooled_output)
            adapted_output = pooled_output + adapted_output  # Residual connection
        else:
            adapted_output = pooled_output
        
        # Generate predictions
        sentiment_logits = self.sentiment_classifier(adapted_output)
        emotion_logits = self.emotion_classifier(adapted_output)
        intensity = self.intensity_regressor(adapted_output)
        
        return {
            'sentiment_logits': sentiment_logits,
            'emotion_logits': emotion_logits,
            'intensity': intensity,
            'embeddings': adapted_output
        }
    
    def _mean_pooling(self, hidden_states: torch.Tensor, 
                     attention_mask: torch.Tensor) -> torch.Tensor:
        """Apply mean pooling with attention mask."""
        attention_mask_expanded = attention_mask.unsqueeze(-1).expand(hidden_states.size())
        masked_hidden = hidden_states * attention_mask_expanded
        sum_hidden = torch.sum(masked_hidden, dim=1)
        sum_mask = torch.sum(attention_mask_expanded, dim=1)
        return sum_hidden / torch.clamp(sum_mask, min=1e-9)

class SentimentAnalysisService:
    """Service for sentiment analysis of GitHub content."""
    
    def __init__(self, model_path: str, device: str = 'auto'):
        self.device = self._get_device(device)
        self.model = self._load_model(model_path)
        self.tokenizer = AutoTokenizer.from_pretrained(model_path)
        
        # Label mappings
        self.sentiment_labels = ['negative', 'neutral', 'positive']
        self.emotion_labels = [
            'anger', 'fear', 'sadness', 'joy', 'surprise', 
            'disgust', 'trust', 'anticipation'
        ]
    
    async def analyze_sentiment(self, texts: List[Dict[str, Any]], 
                              domain: str = 'issues') -> Dict[str, Any]:
        """Analyze sentiment for a collection of texts."""
        if not texts:
            return self._empty_sentiment_result()
        
        # Prepare text content
        text_content = [self._extract_text(item) for item in texts]
        
        # Batch process texts
        results = []
        batch_size = 16
        
        for i in range(0, len(text_content), batch_size):
            batch_texts = text_content[i:i + batch_size]
            batch_results = await self._process_batch(batch_texts, domain)
            results.extend(batch_results)
        
        # Aggregate results
        return self._aggregate_sentiment_results(results, texts)
    
    async def _process_batch(self, texts: List[str], domain: str) -> List[Dict[str, Any]]:
        """Process a batch of texts."""
        # Tokenize batch
        tokens = self.tokenizer(
            texts,
            max_length=512,
            truncation=True,
            padding=True,
            return_tensors='pt'
        ).to(self.device)
        
        # Run inference
        with torch.no_grad():
            outputs = self.model(
                input_ids=tokens['input_ids'],
                attention_mask=tokens['attention_mask'],
                domain=domain
            )
        
        # Process outputs
        sentiment_probs = torch.softmax(outputs['sentiment_logits'], dim=1).cpu().numpy()
        emotion_probs = torch.softmax(outputs['emotion_logits'], dim=1).cpu().numpy()
        intensities = outputs['intensity'].cpu().numpy()
        
        # Format results
        batch_results = []
        for i in range(len(texts)):
            batch_results.append({
                'sentiment_scores': dict(zip(self.sentiment_labels, sentiment_probs[i])),
                'emotion_scores': dict(zip(self.emotion_labels, emotion_probs[i])),
                'intensity': float(intensities[i][0]),
                'predicted_sentiment': self.sentiment_labels[sentiment_probs[i].argmax()],
                'predicted_emotion': self.emotion_labels[emotion_probs[i].argmax()]
            })
        
        return batch_results
```

## Trend Prediction Model

### Time Series Transformer for Repository Trends

```python
# aiml/models/trend_prediction.py
import torch
import torch.nn as nn
import math
from typing import Dict, Any, List, Tuple, Optional
import numpy as np

class PositionalEncoding(nn.Module):
    """Positional encoding for time series data."""
    
    def __init__(self, d_model: int, max_len: int = 5000):
        super().__init__()
        
        pe = torch.zeros(max_len, d_model)
        position = torch.arange(0, max_len, dtype=torch.float).unsqueeze(1)
        div_term = torch.exp(torch.arange(0, d_model, 2).float() * 
                           (-math.log(10000.0) / d_model))
        
        pe[:, 0::2] = torch.sin(position * div_term)
        pe[:, 1::2] = torch.cos(position * div_term)
        pe = pe.unsqueeze(0).transpose(0, 1)
        
        self.register_buffer('pe', pe)
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        return x + self.pe[:x.size(0), :]

class TrendPredictionTransformer(nn.Module):
    """Transformer-based model for predicting repository trends."""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__()
        self.config = config
        
        # Model dimensions
        self.d_model = config.get('d_model', 256)
        self.nhead = config.get('nhead', 8)
        self.num_layers = config.get('num_layers', 6)
        self.seq_len = config.get('sequence_length', 90)  # 90 days
        
        # Input projection
        input_dim = config.get('input_features', 10)
        self.input_projection = nn.Linear(input_dim, self.d_model)
        
        # Positional encoding
        self.pos_encoder = PositionalEncoding(self.d_model, max_len=self.seq_len)
        
        # Transformer encoder
        encoder_layer = nn.TransformerEncoderLayer(
            d_model=self.d_model,
            nhead=self.nhead,
            dim_feedforward=self.d_model * 4,
            dropout=0.1,
            activation='gelu',
            batch_first=True
        )
        self.transformer_encoder = nn.TransformerEncoder(
            encoder_layer, 
            num_layers=self.num_layers
        )
        
        # Prediction heads
        self.trend_predictor = nn.Sequential(
            nn.Linear(self.d_model, 128),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(128, config.get('prediction_horizon', 30))  # 30 days ahead
        )
        
        self.trend_classifier = nn.Sequential(
            nn.Linear(self.d_model, 128),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(128, 3)  # declining, stable, growing
        )
        
        self.volatility_predictor = nn.Sequential(
            nn.Linear(self.d_model, 64),
            nn.ReLU(),
            nn.Linear(64, 1),
            nn.Softplus()  # Ensure positive volatility
        )
    
    def forward(self, x: torch.Tensor, mask: Optional[torch.Tensor] = None) -> Dict[str, torch.Tensor]:
        """Forward pass for trend prediction."""
        batch_size, seq_len, _ = x.shape
        
        # Project input to model dimension
        x = self.input_projection(x)
        
        # Add positional encoding
        x = x.transpose(0, 1)  # (seq_len, batch_size, d_model)
        x = self.pos_encoder(x)
        x = x.transpose(0, 1)  # (batch_size, seq_len, d_model)
        
        # Apply transformer encoder
        if mask is not None:
            # Create attention mask for transformer
            attn_mask = mask.unsqueeze(1).expand(-1, seq_len, -1)
        else:
            attn_mask = None
        
        encoded = self.transformer_encoder(x, src_key_padding_mask=attn_mask)
        
        # Use last time step for predictions
        last_hidden = encoded[:, -1, :]
        
        # Generate predictions
        trend_values = self.trend_predictor(last_hidden)
        trend_class = self.trend_classifier(last_hidden)
        volatility = self.volatility_predictor(last_hidden)
        
        return {
            'trend_predictions': trend_values,
            'trend_classification': trend_class,
            'volatility': volatility,
            'encoded_features': encoded
        }

class TrendPredictionService:
    """Service for predicting repository trends."""
    
    def __init__(self, model_path: str, device: str = 'auto'):
        self.device = self._get_device(device)
        self.model = self._load_model(model_path)
        self.feature_scaler = self._load_scaler(model_path)
        
        # Trend labels
        self.trend_labels = ['declining', 'stable', 'growing']
    
    async def predict_trends(self, time_series_data: Dict[str, List[float]], 
                           prediction_horizon: int = 30) -> Dict[str, Any]:
        """Predict repository trends based on historical data."""
        
        # Prepare features
        features = self._prepare_features(time_series_data)
        
        # Scale features
        scaled_features = self.feature_scaler.transform(features)
        
        # Convert to tensor
        feature_tensor = torch.tensor(
            scaled_features, dtype=torch.float32
        ).unsqueeze(0).to(self.device)
        
        # Run inference
        with torch.no_grad():
            outputs = self.model(feature_tensor)
        
        # Process predictions
        return self._process_trend_predictions(outputs, prediction_horizon)
    
    def _prepare_features(self, data: Dict[str, List[float]]) -> np.ndarray:
        """Prepare time series features for the model."""
        # Expected features: stars, forks, issues, commits, contributors, etc.
        feature_names = [
            'stars', 'forks', 'issues_opened', 'issues_closed',
            'commits', 'contributors', 'pull_requests', 'releases',
            'code_frequency', 'activity_score'
        ]
        
        # Ensure all series have the same length
        min_length = min(len(data.get(name, [])) for name in feature_names)
        
        features = []
        for name in feature_names:
            series = data.get(name, [0] * min_length)
            # Take the last min_length values
            features.append(series[-min_length:])
        
        # Transpose to get (time_steps, features)
        return np.array(features).T
```

This comprehensive model implementations documentation provides detailed architectures and implementations for the core AI/ML models used in GitInsight, enabling sophisticated analysis of code quality, sentiment, and trends in GitHub repositories.
